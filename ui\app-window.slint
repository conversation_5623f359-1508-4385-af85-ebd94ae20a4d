// Import only the widgets we actually need from std-widgets
import { Button, ComboBox, LineEdit, ScrollView, VerticalBox, HorizontalBox, GroupBox } from "std-widgets.slint";

export component App inherits Window {
    title: "Rust + Slint Serial Tool";
    width: 600px;
    height: 400px;

    // --- Properties & Models ---
    // The model for the ComboBox is now a simple array of strings.
    in-out property <[string]> port-list: [];
    in-out property <string> connection-status: "Disconnected";
    in-out property <string> received-data: "";
    in property <bool> is-connected: false;

    // --- Callbacks ---
    // This is the correct syntax for defining a callback.
    callback refresh-ports();
    callback connect-clicked(port: string, baud_rate: string);
    callback send-clicked(data: string);

    GridLayout {
        padding: 10px;
        spacing: 10px;

        Row {
            // ==== Connection Controls ====
            GroupBox {
                title: "Connection";
                VerticalBox {
                    spacing: 5px;
                    HorizontalBox {
                        spacing: 5px;
                        port-combo-box := ComboBox {
                            model: root.port-list; // This now correctly binds to a [string] model
                            horizontal-stretch: 2;
                            height: 30px;
                        }
                        But<PERSON> {
                            text: "Refresh";
                            height: 30px;
                            // Correct syntax for invoking the callback
                            clicked => { refresh-ports(); }
                        }
                    }
                    baud-rate-input := LineEdit {
                        text: "9600";
                        height: 30px;
                        placeholder-text: "Baud Rate";
                    }
                    connect-button := Button {
                        text: root.is-connected ? "Disconnect" : "Connect";
                        primary: !root.is-connected;
                        height: 30px;
                        clicked => {
                            root.connect-clicked(port-combo-box.current-value, baud-rate-input.text);
                        }
                    }
                    // Text is a built-in element, no import needed.
                    Text { text: root.connection-status; }
                }
            }
        }
        Row {
            // ==== Data Display and Sending ====
            VerticalBox {
                spacing: 5px;
                ScrollView {
                    viewport-height: 200px;
                    Text {
                        text: root.received-data;
                        wrap: word-wrap;
                    }
                }
                HorizontalBox {
                    spacing: 5px;
                    send-input := LineEdit {
                        placeholder-text: "Type data to send...";
                        height: 30px;
                        horizontal-stretch: 3;
                    }
                    Button {
                        text: "Send";
                        height: 30px;
                        enabled: root.is-connected;
                        clicked => {
                            root.send-clicked(send-input.text);
                            send-input.text = "";
                        }
                    }
                }
            }
        }
    }
}