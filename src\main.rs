use std::io::{self, Read, Write};
use std::sync::atomic::{AtomicBool, Ordering};
use std::sync::{mpsc, Arc, Mutex};
use std::thread;
use std::time::Duration;

use slint::{ModelRc, SharedString, VecModel};

// Include the compiled slint UI. This will now use the corrected .slint file.
slint::include_modules!();

fn main() -> Result<(), slint::PlatformError> {
    let ui = App::new()?;

    // --- State Management (Unchanged) ---
    let serial_port = Arc::new(Mutex::new(None::<Box<dyn serialport::SerialPort>>));
    let stop_signal = Arc::new(AtomicBool::new(false));
    let (tx, rx) = mpsc::channel::<String>();

    // --- Initial UI Setup (Modified) ---
    // We now create a Vec of SharedString, not PortInfo.
    let ports = serialport::available_ports().unwrap_or_else(|_| {
        vec![serialport::SerialPortInfo {
            port_name: "No ports found".into(),
            port_type: serialport::SerialPortType::Unknown,
        }]
    });
    // Convert the list of port infos into a Vec of SharedString for the Slint model.
    let port_names: Vec<SharedString> = ports
        .into_iter()
        .map(|p| p.port_name.into())
        .collect();
    ui.set_port_list(ModelRc::new(VecModel::from(port_names)));


    // --- UI Callbacks (Modified) ---

    let ui_handle = ui.as_weak();
    ui.on_refresh_ports(move || {
        let ui = ui_handle.unwrap();
        let ports = serialport::available_ports().unwrap_or_default();
        // Convert to Vec<SharedString> here as well.
        let port_names: Vec<SharedString> = ports
            .into_iter()
            .map(|p| p.port_name.into())
            .collect();
        ui.set_port_list(ModelRc::new(VecModel::from(port_names)));
        println!("Ports refreshed.");
    });
    
    // The rest of the file is identical to the previous version
    let ui_handle = ui.as_weak();
    let serial_port_clone = Arc::clone(&serial_port);
    let stop_signal_clone = Arc::clone(&stop_signal);
    ui.on_connect_clicked(move |port_name, baud_rate| {
        let ui = ui_handle.unwrap();
        if ui.get_is_connected() {
            // --- Disconnect Logic ---
            println!("Disconnecting...");
            stop_signal_clone.store(true, Ordering::Relaxed);
            *serial_port_clone.lock().unwrap() = None;
            ui.set_is_connected(false);
            ui.set_connection_status("Disconnected".into());
        } else {
            // --- Connect Logic ---
            let baud_rate_val = baud_rate.parse::<u32>().unwrap_or(9600);
            println!("Connecting to {} at {} baud", port_name, baud_rate_val);

            match serialport::new(port_name.as_str(), baud_rate_val)
                .timeout(Duration::from_millis(10))
                .open()
            {
                Ok(port) => {
                    *serial_port_clone.lock().unwrap() = Some(port);
                    ui.set_is_connected(true);
                    ui.set_connection_status(format!("Connected to {}", port_name).into());
                    stop_signal_clone.store(false, Ordering::Relaxed);

                    // --- Spawn Reader Thread ---
                    let mut port_clone = serial_port_clone.lock().unwrap().as_mut().unwrap().try_clone().expect("Failed to clone port");
                    let stop_signal_thread = Arc::clone(&stop_signal_clone);
                    let tx_thread = tx.clone();
                    
                    thread::spawn(move || {
                        println!("Reader thread started.");
                        let mut buffer = [0; 128];
                        while !stop_signal_thread.load(Ordering::Relaxed) {
                            match port_clone.read(&mut buffer) {
                                Ok(bytes) => {
                                    if bytes > 0 {
                                        let received = String::from_utf8_lossy(&buffer[..bytes]).to_string();
                                        if tx_thread.send(received).is_err() {
                                            eprintln!("Failed to send data to UI thread.");
                                            break;
                                        }
                                    }
                                }
                                Err(ref e) if e.kind() == io::ErrorKind::TimedOut => { /* Continue */ }
                                Err(e) => {
                                    eprintln!("Serial read error: {}", e);
                                    break;
                                }
                            }
                        }
                        println!("Reader thread finished.");
                    });
                }
                Err(e) => {
                    eprintln!("Failed to open port: {}", e);
                    ui.set_connection_status("Connection Failed".into());
                }
            }
        }
    });
    
    let ui_handle = ui.as_weak();
    let serial_port_clone = Arc::clone(&serial_port);
    ui.on_send_clicked(move |data| {
        let ui = ui_handle.unwrap();
        if ui.get_is_connected() {
            if let Some(port) = &mut *serial_port_clone.lock().unwrap() {
                if let Err(e) = port.write_all(data.as_bytes()) {
                    eprintln!("Failed to write to port: {}", e);
                } else {
                    let log_text = format!("> {}\n", data);
                    let current_text = ui.get_received_data();
                    ui.set_received_data(format!("{}{}", current_text, log_text).into());
                }
            }
        }
    });

    // --- UI Update Timer ---
    let ui_handle = ui.as_weak();
    let timer = slint::Timer::default();
    timer.start(slint::TimerMode::Repeated, std::time::Duration::from_millis(50), move || {
        if let Ok(data) = rx.try_recv() {
            if let Some(ui) = ui_handle.upgrade() {
                let current_text = ui.get_received_data();
                ui.set_received_data(format!("{}{}", current_text, data).into());
            }
        }
    });
    
    ui.run()
}